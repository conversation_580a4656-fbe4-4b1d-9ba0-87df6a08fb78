<template>
  <q-page>
    <q-table
      style="height: calc(100vh - 50px)"
      ref="tableRef"
      binary-state-sort
      bordered
      flat
      virtual-scroll
      row-key="Rid"
      selection="multiple"
      :rows="finalRows"
      :columns="columns"
      :filter="filter"
      v-model:selected="selected"
      v-model:pagination="pagination"
      :rows-per-page-options="[0]"
      @selection="handleSelection"
      class="!border-x-0 !border-t-0 !rounded-none sticky-header-table"
    >
      <template v-slot:top>
        <q-btn
          color="primary"
          :label="$t('form.add')"
          @click="openDialogBeforeAction(OpenDialogAction.Add)"
        />
        <q-btn
          class="q-ml-sm"
          color="negative"
          :label="$t('form.batchDelete')"
          :disable="selected.length <= 0"
          @click="batchDeleteRows(selected)"
        />
        <q-space />
        <q-input
          dense
          debounce="300"
          v-model="filter"
          :placeholder="$t('form.search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>

      <template v-slot:header-selection="scope">
        <q-checkbox v-model="scope.selected">
          <q-tooltip
            anchor="top middle"
            :offset="[10, 20]"
          >
            {{ $t('dbUser.selectTooltip') }}
          </q-tooltip>
        </q-checkbox>
      </template>

      <template v-slot:body-selection="scope">
        <q-checkbox
          :model-value="scope.selected"
          @update:model-value="(val, evt) => {
            // @ts-ignore
            Object.getOwnPropertyDescriptor(scope, 'selected').set(val, evt)
          }"
        />
      </template>

      <template v-slot:body-cell-Token="scope">
        <q-td align="center">
          <span class="mr-2" v-if="scope.row.Token">{{ `...${scope.row.Token.slice(-8)}` }}</span>
          <q-btn flat dense size="xs" icon="content_copy" @click="copyTextToClipboard(scope.row.Token)" :disable="getDisableEditAdminMapToken(scope.row)" v-if="scope.row.Token">
            <q-tooltip v-if="!getDisableEditAdminMapToken(scope.row)">
              {{ $t('dbMapProviderToken.clickToCopyApiKey') }}
            </q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <template v-slot:body-cell-Status="props">
        <q-td auto-width align="center">
          <template v-if="props.row.Status === 1">
            <q-chip
              dense
              clickable
              color="teal"
              text-color="white"
              icon="star"
              :disable="getDisableEditAdminMapToken(props.row)"
            >
              {{ $t('dbProjectToken.enable') }}
              <popup-proxy-confirm
                  v-if="!getDisableEditAdminMapToken(props.row)"
                :message="$t('dbProjectToken.wantToDisabledToken')"
                @confirm="toggleDbMapProviderTokenStatus(props.row, 4)"
              />
            </q-chip>
          </template>
          <template v-else>
            <q-chip
              dense
              clickable
              color="red"
              text-color="white"
              icon="star_border"
              :disable="getDisableEditAdminMapToken(props.row)"
            >
              {{ $t('dbProjectToken.disable') }}
              <popup-proxy-confirm
                  v-if="!getDisableEditAdminMapToken(props.row)"
                :message="$t('dbProjectToken.wantToEnableToken')"
                @confirm="toggleDbMapProviderTokenStatus(props.row, 1)"
              />
            </q-chip>
          </template>
        </q-td>
      </template>

      <template v-slot:body-cell-Note="props">
        <q-td auto-width align="center">
         <OverflowTd :value="props.row.Note"></OverflowTd>
        </q-td>
      </template>

      <template v-slot:body-cell-operate="props">
        <q-td auto-width>
          <q-btn
            round
            outline
            icon="edit"
            size="xs"
            color="primary"
            :disable="getDisableEditAdminMapToken(props.row)"
            @click="openDialogBeforeAction(OpenDialogAction.Edit, props.row)"
          >
            <q-tooltip>
              {{ $t('form.edit') }}
            </q-tooltip>
          </q-btn>
          <q-btn
            round
            outline
            icon="delete"
            size="xs"
            color="negative"
            class="!ml-2"
            :disable="getDisableEditAdminMapToken(props.row)"
          >
            <q-tooltip>
              {{ $t('form.delete') }}
            </q-tooltip>
            <popup-proxy-confirm
              :message="$t('dbProjectToken.wantToDeleteToken')"
              @confirm="deleteDbMapProviderToken(props.row)"
            />
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data>
        <div class="full-width flex justify-center">
          <q-icon
            name="warning"
            size="xs"
          ></q-icon>
          <span>{{ $t('dbUser.noData') }}</span>
        </div>
      </template>
    </q-table>

    <q-dialog
      v-model="visible"
      persistent
    >
      <q-card class="w-fit !max-w-none">
        <!--header-->
        <q-bar class="bg-white !px-4 mt-2">
          <q-icon
            name="api"
            size="24px"
            color="primary"
          />
          <span class="pl-2">{{ `${$t('pages.dbMapProviderToken')}-${dialogAction === OpenDialogAction.Add ?
            $t('form.add')
            : $t('form.edit')}` }}</span>
          <q-space></q-space>
          <q-btn
            flat
            round
            dense
            icon="close"
            size="sm"
            v-close-popup
          />
        </q-bar>

        <!--body-->
        <q-card-section class="w-full">
          <q-form
            class="w-full grid grid-cols-1 gap-2 user-form"
            :class="{ 'min-w-xl': !isMobile }"
            ref="formRef"
          >
            <q-input
                v-model="dbMapProviderToken.Name"
                :label="$t('dbMapProviderToken.apiName')"
                outlined
                dense
                clearable
                lazy-rules
                :maxlength="16"
                :rules="[rules.required, rules.nameUnique]"
            />
            <q-select
              outlined
              dense
              emit-value
              map-options
              options-dense
              v-model="dbMapProviderToken.Provider"
              :options="availableMapProviderOption"
              :rules="[rules.requiredMapPlatform]"
              :label="$t('dbMapProviderToken.mapPlatform')"
            />

            <q-input
              v-if="!isLocalDirectoryProvider"
              v-model="dbMapProviderToken.Token"
              :label="$t('dbMapProviderToken.apiKey')"
              outlined
              dense
              lazy-rules
              :rules="[rules.required]"
              :maxlength="128"
              clearable
              :hint="$t('dbMapProviderToken.apiKeyByMapPlatform')"
              v-clear-fix="[dbMapProviderToken, 'Token']"
            />

            <q-input
                v-model.number="dbMapProviderToken.Priority"
                :label="$t('dbMapProviderToken.priority')"
                outlined
                dense
                lazy-rules
                :rules="[rules.priority]"
                type="number"
                clearable
                @clear="dbMapProviderToken.Priority = 1"
              />

            <q-input
                v-model.number="dbMapProviderToken.BaseUrl"
                :label="getBaseUrlLabel"
                outlined
                dense
                clearable
                class="pb-4"
                :rules="[rules.requiredBaseUrl]"
                v-clear-fix="[dbMapProviderToken, 'BaseUrl']"
            >
              <template v-slot:prepend v-if="isLocalDirectoryProvider">
                <q-icon
                  name="info"
                  color="primary"
                  size="sm"
                  class="cursor-pointer"
                >
                  <q-tooltip
                    anchor="top middle"
                    self="bottom middle"
                    :offset="[10, 10]"
                    class="bg-white text-black shadow-4 text-body2"
                    style="white-space: pre-line; max-width: 400px;"
                  >
                    <div class="text-weight-medium mb-2">{{ $t('dbMapProviderToken.directoryStructure.title') }}</div>
                    <div>{{ getDirectoryStructureTooltip }}</div>
                  </q-tooltip>
                </q-icon>
              </template>
            </q-input>

            <language-selector
                v-if="isLocalDirectoryProvider"
                v-model="dbMapProviderToken.Language"
                :clear-fix-params="[dbMapProviderToken, 'Language']"
            />

            <q-input
              v-model="dbMapProviderToken.Note"
              :label="$t('dbUser.note')"
              type="textarea"
              autogrow
              outlined
              dense
              clearable
              lazy-rules
              :maxlength="0xFF"
              class="pb-4"
              v-clear-fix="[dbMapProviderToken, 'Note']"
            />

            <div class="rounded-borders" v-if="dbMapProviderToken.Provider === MapProviderEnum.ProviderGoogle">
              <div class="text-ellipsis mb-2">{{ $t('dbMapProviderToken.googleMapType') }}</div>
              <q-option-group
                  v-model="dbMapProviderToken.GoogleMapApi"
                  :options="googleMapTypeOptions"
                  type="radio"
                  inline
                  dense
              />
            </div>

            <div class="flex items-center justify-start pb-1 gap-3">
              <q-checkbox
                v-model="foreverExpireTime"
                :label="$t('dbProjectToken.alwaysValid')"
                dense
                class="flex-none w-fit"
              />
              <q-input
                v-if="!foreverExpireTime"
                outlined
                dense
                hide-bottom-space
                v-model="apiExpireTime"
                :rules="[rules.required]"
                :label="$t('dbProjectToken.expiryDate')"
                class="flex-auto"
              >
                <template v-slot:prepend>
                  <q-icon
                    name="event"
                    class="cursor-pointer"
                  >
                    <q-popup-proxy
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-date
                        v-model="apiExpireTime"
                        mask="YYYY-MM-DD HH:mm"
                      />
                    </q-popup-proxy>
                  </q-icon>
                </template>

                <template v-slot:append>
                  <q-icon
                    name="access_time"
                    class="cursor-pointer"
                  >
                    <q-popup-proxy
                      cover
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-time
                        v-model="apiExpireTime"
                        mask="YYYY-MM-DD HH:mm"
                        format24h
                      >
                      </q-time>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>

            <q-checkbox
                v-model="dbMapProviderToken.IsUseProxy"
                :label="$t('dbMapProviderToken.isUseProxy')"
                dense
                class="w-fit"
            />

            <q-checkbox
              v-model="dbMapProviderToken.Status"
              :label="$t('dbProjectToken.wantToEnableToken')"
              :true-value="1"
              :false-value="4"
              dense
              class="w-fit"
            />

            <div v-if="!isLocalDirectoryProvider" :class="{'grid grid-cols-2 gap-3': !isMobile }">
              <div class="mt-3 rounded-borders">
                <div class="text-ellipsis mb-2">{{ $t('dbMapProviderToken.quotaCalcMethod') }}</div>
                <q-option-group
                  v-model="dbMapProviderToken.QuotasType"
                  :options="quotasTypeOptions"
                  type="radio"
                  inline
                  dense
                />
              </div>

              <q-input
                v-model.number="dbMapProviderToken.QuotasLimit"
                :label="$t('dbProject.quotasLimit')"
                :rules="[rules.quotasLimit]"
                type="number"
                :hint="$t('dbMapProviderToken.quotasUnlimited')"
              />
            </div>
          </q-form>
        </q-card-section>

        <!--footer-->
        <q-card-actions class="flex justify-center gap-4">
          <q-btn
            color="primary"
            class="!w-[150px]"
            :label="dialogAction === OpenDialogAction.Add ? $t('form.add') : $t('form.confirm')"
            @click=" submit(false)"
          />
          <q-btn
            v-if="dialogAction === OpenDialogAction.Add"
            color="secondary"
            class="!w-[150px]"
            :label="$t('form.keepAdd')"
            @click="submit(true)"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import LanguageSelector from '@/components/LanguageSelector.vue'
import OverflowTd from '@/components/OverflowTd.vue'
import { SetProviderTokenReqSchema } from '@/proto/bfmap.rpc_pb'
import {
  type DbMapProviderToken,
  DbMapProviderTokenSchema,
  GoogleMapApiEnum,
  MapProviderEnum
} from '@/proto/db_pb.ts'
import { setProviderToken } from '@/services/connectRpc'
import { AdminUserRid } from '@/stores/common.ts'
import { useDbMapProviderTokenStore, useDbMapProviderUsedQuotasStore } from '@/stores/dataBase.ts'
import { useLoginStatusStore } from '@/stores/session.ts'
import { confirmAgainBatchDelete, useTableHandleSelection } from '@/utils/common.ts'
import { copyTextToClipboard } from '@/utils/crypto.ts'
import {
  addDateTime,
  bigIntUnixTimeToString,
  dateTimeStringToDayjs,
  dayjs,
  formatDayjs,
  getTimestamp
} from '@/utils/dayjs.ts'
import { errorMessage, successMessage } from '@/utils/notify'
import { NewMapApiKeyAction } from '@/utils/pubSubSubject'
import { OpenDialogAction } from '@/utils/types'
import * as validation from '@/utils/validation.ts'
import { create } from '@bufbuild/protobuf'
import cloneDeep from 'lodash/cloneDeep'
import { storeToRefs } from 'pinia'
import { QForm, QPage, QTable, type QTableColumn, useQuasar } from 'quasar'
import { v7 as uuidV7 } from 'uuid'
import { computed, customRef, defineAsyncComponent, onMounted, onUnmounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { StrPubSub } from 'ypubsub'

  const PopupProxyConfirm = defineAsyncComponent(() => import('@/components/PopupProxyConfirm.vue'))

  const { t } = useI18n()
  const $q = useQuasar()
  const dbProjectTokenStore = useDbMapProviderTokenStore()
  const { rows } = storeToRefs(dbProjectTokenStore)
  const { addRow, updateRow: updateDbMapProviderTokenStoreRow, deleteRow: deleteDbMapProviderTokenStoreRow } = dbProjectTokenStore
  const dbMapProviderUsedQuotasStore = useDbMapProviderUsedQuotasStore()
  const { rows: dbMapProviderUsedQuotasRows } = storeToRefs(dbMapProviderUsedQuotasStore)
  const loginStatusStore = useLoginStatusStore()
  const { userRid } = storeToRefs(loginStatusStore)
  const getDefaultDbMapProviderToken = (initValue?: DbMapProviderToken): DbMapProviderToken => {
    return create(DbMapProviderTokenSchema, {
      Rid: uuidV7(),
      Provider: initValue?.Provider ?? MapProviderEnum.ProviderGoogle,
      UserRid: userRid.value,
      // Token为谷歌地图、天地图等token
      Token: '',
      Name: '',
      CreateTime: getTimestamp(),
      Note: initValue?.Note ?? '',
      Setting: '{}',
      ExpireTime: initValue?.ExpireTime ?? 0n,
      Status: initValue?.Status ?? 1,
      GoogleMapApi: GoogleMapApiEnum.GoogleMapApiStatic,
      QuotasType: initValue?.QuotasType ?? 4,
      QuotasLimit: initValue?.QuotasLimit ?? 0,
      Priority: 1,
      BaseUrl: '',
      IsUseProxy: true,
      Language: '',
    })
  }

  const { tableRef, selected, handleSelection } = useTableHandleSelection<DbMapProviderToken>()

  const finalRows = computed(() => {
    return rows.value.filter(row => row.Status !== 8)
  })
  const dialogAction = ref<OpenDialogAction>(OpenDialogAction.Add)
  const formRef = ref<QForm>()
  const visible = ref<boolean>(false)
  const dbMapProviderToken = ref<DbMapProviderToken>(getDefaultDbMapProviderToken())
  const apiExpireTime = customRef<string>((track: () => void, trigger: () => void) => ({
    get: () => {
      track()
      try {
        return bigIntUnixTimeToString(dbMapProviderToken.value.ExpireTime, 'YYYY-MM-DD HH:mm')
      } catch {
        return ''
      }

    },
    set: (value: string) => {
      try {
        dbMapProviderToken.value.ExpireTime = value ? BigInt(dateTimeStringToDayjs(value).unix()) : 0n
      } catch {
        dbMapProviderToken.value.ExpireTime = 0n
      }

      trigger()
    }
  }))
  const foreverExpireTime = customRef<boolean>((track: () => void, trigger: () => void) => ({
    get: () => {
      track()
      return dbMapProviderToken.value.ExpireTime === 0n
    },
    set: (value: boolean) => {
      if (value) {
        dbMapProviderToken.value.ExpireTime = 0n
      } else {
        dbMapProviderToken.value.ExpireTime = BigInt(addDateTime(dayjs.utc(), 12, 'month').unix())
      }
      trigger()
    }
  }))
  const availableMapProviderOption = computed(() => {
    return [
      {
        label: t('dbProjectToken.googleMap'),
        value: MapProviderEnum.ProviderGoogle
      },
      {
        label: t('dbProjectToken.tianditu'),
        value: MapProviderEnum.ProviderTianditu
      },
      {
        label: t('dbProjectToken.openStreetMap'),
        value: MapProviderEnum.ProviderOSM
      },
      {
        label: t('dbProjectToken.googleMap') + ': ' + t('dbProjectToken.localDirectory'),
        value: MapProviderEnum.ProviderGoogleLocalDirectory
      },
      {
        label: t('dbProjectToken.tianditu') + ': ' + t('dbProjectToken.localDirectory'),
        value: MapProviderEnum.ProviderTiandituLocalDirectory
      },
      {
        label: t('dbProjectToken.openStreetMap') + ': ' + t('dbProjectToken.localDirectory'),
        value: MapProviderEnum.ProviderOSMLocalDirectory
      },
    ]
  })
  const quotasTypeOptions = computed(() => {
    return [
      {
        label: t('dbProject.quotasCalculateBy', { type: t('dbProject.day') }),
        value: 3
      },
      {
        label: t('dbProject.quotasCalculateBy', { type: t('dbProject.month') }),
        value: 4
      },
    ]
  })

  const isMobile = computed(() => {
    return $q.platform.is.mobile
  })

  const isLocalDirectoryProvider = computed(() => {
    return dbMapProviderToken.value.Provider === MapProviderEnum.ProviderGoogleLocalDirectory ||
           dbMapProviderToken.value.Provider === MapProviderEnum.ProviderTiandituLocalDirectory ||
           dbMapProviderToken.value.Provider === MapProviderEnum.ProviderOSMLocalDirectory
  })

  const getDisableEditAdminMapToken = (row: DbMapProviderToken) => {
    return row.UserRid !== loginStatusStore.userRid && loginStatusStore.userRid !== AdminUserRid
  }

  const googleMapTypeOptions = computed(() => {
    return [
      {
        label: t('dbMapProviderToken.mapApiStatic'),
        value: 0
      },
      {
        label: t('dbMapProviderToken.mapApiTile'),
        value: 1
      },
      {
        label: t('dbMapProviderToken.mapApiAll'),
        value: 2
      },
    ]
  })

  const filter = ref('')
  const pagination = ref({
    rowsPerPage: 0
  })

  const columns = computed<QTableColumn[]>(() => {
    return [
      {
        name:'Name',
        label: t('dbMapProviderToken.apiKey'),
        align: 'center',
        field: 'Name',
      },
      {
        name: 'Provider',
        label: t('dbMapProviderToken.mapPlatform'),
        align: 'center',
        field: 'Provider',
        sortable: true,
        format: (value: MapProviderEnum) => {
          return availableMapProviderOption.value.find(item => item.value === value)?.label ?? ('' + value)
        },
      },
      {
        name: 'Token',
        label: t('dbProjectToken.apiKey'),
        align: 'center',
        field: 'Token',
      },
      {
        name: 'Status',
        label: t('dbProjectToken.status'),
        align: 'center',
        field: 'Status',
      },
      {
        name: 'Priority',
        label: t('dbMapProviderToken.priority'),
        align: 'center',
        field: 'Priority',
      },
      {
        name: 'QuotasType',
        label: t('dbProject.quotasType'),
        align: 'center',
        field: 'QuotasType',
        format: (val: number) => {
          return quotasTypeOptions.value.find(type => type.value === val)?.label ?? val + ''
        }
      },
      {
        name: 'quotasLimitCount',
        label: t('dbProject.usedQuotas') + ' / ' + t('dbProject.quotasLimit'),
        align: 'center',
        field: 'QuotasLimit',
        format: (val: number,row: DbMapProviderToken) => {
          return (dbMapProviderUsedQuotasRows.value.find(r => r.Rid === row.Rid)?.UsedQuotas ?? 0) + ' / ' + getQuotasLimitLabel(val)
        }
      },
      {
        name: 'CreateTime',
        label: t('dbUser.createTime'),
        align: 'center',
        field: 'CreateTime',
        sortable: true,
        format: (value: bigint) => {
          return formatDayjs(Number(value) * 1000)
        },
      },
      {
        name: 'ExpireTime',
        label: t('dbProjectToken.expiryDate'),
        align: 'center',
        field: 'ExpireTime',
        sortable: true,
        format: (value: bigint) => {
          return value === 0n ? t('dbProjectToken.alwaysValid') : formatDayjs(Number(value) * 1000, 'YYYY-MM-DD HH:mm')
        },
      },
      {
        name: 'Note',
        label: t('dbUser.note'),
        align: 'center',
        field: 'Note',
      },
      {
        name: 'operate',
        label: t('dbUser.operate'),
        align: 'center',
        field: ''
      },
    ]
  })

  const getQuotasLimitLabel = (quotasLimit: number): string => {
    if (quotasLimit === 0) {
      return t('dbProject.unlimited')
    }
    return quotasLimit + ''
  }
  const getBaseUrlLabel = computed(() => {

    const providerType = MapProviderEnum[dbMapProviderToken.value.Provider]
    const providerName = providerType.split('Provider')[1]

    switch (providerName) {
      case 'Google':
        return t('dbMapProviderToken.baseUrl.google')
      case 'Tianditu':
        return t('dbMapProviderToken.baseUrl.tianditu')
      case 'OSM':
        return t('dbMapProviderToken.baseUrl.osm')
      case 'GoogleLocalDirectory':
        return t('dbMapProviderToken.baseUrl.googleLocal')
      case 'TiandituLocalDirectory':
        return t('dbMapProviderToken.baseUrl.tiandituLocal')
      case 'OSMLocalDirectory':
        return t('dbMapProviderToken.baseUrl.osmLocal')
      default:
        return t('dbMapProviderToken.baseUrl.default')
    }
  })

  const getDirectoryStructureTooltip = computed(() => {
    const providerType = MapProviderEnum[dbMapProviderToken.value.Provider]
    const providerName = providerType.split('Provider')[1]

    switch (providerName) {
      case 'GoogleLocalDirectory':
        return t('dbMapProviderToken.directoryStructure.googleLocal')
      case 'TiandituLocalDirectory':
        return t('dbMapProviderToken.directoryStructure.tiandituLocal')
      case 'OSMLocalDirectory':
        return t('dbMapProviderToken.directoryStructure.osmLocal')
      default:
        return ''
    }
  })

  const rules = computed(() => {
    const maxQuotasLimit = 999999999
    return {
      required: (val: string | null | undefined) => {
        if (dbMapProviderToken.value.Provider === MapProviderEnum.ProviderOSM) {
          return true
        }
        return validation.required(val)
      },
      requiredMapPlatform: (val: number | null | undefined) => (val !== null && val !== undefined) || t('validate.required'),
      quotasLimit: (val: number) => (val >= 0 && val <= maxQuotasLimit) || t('validate.numberRange', { min: 0, max: maxQuotasLimit.toLocaleString() }),
      priority: (val: number) => {
        if (val === null || val === undefined) return true
        return (Number.isInteger(val) && val >= 1 && val <= 9999) || t('validate.numberRange', { min: 1, max: 9999 })
      },
      nameUnique: (val: string) => {
        const row = rows.value.find(row => row.Name === val && row.UserRid === loginStatusStore.userRid)
        if (row?.Rid === dbMapProviderToken.value.Rid) {
          return true
        }
        return row ? t('validate.nameUnique') : true
      },
      requiredBaseUrl: (val: string) => {
        if (isLocalDirectoryProvider.value) {
          return validation.required(val)
        }
        return true
      }
    }
  })

  // 打开弹窗前操作
  function openDialogBeforeAction(action: OpenDialogAction, row?: DbMapProviderToken) {
    dialogAction.value = action
    visible.value = true

    if (action === OpenDialogAction.Add) {
      dbMapProviderToken.value = getDefaultDbMapProviderToken()
    } else {
      dbMapProviderToken.value = cloneDeep(row as DbMapProviderToken)
    }
  }

  async function batchDeleteRows(rows: DbMapProviderToken[]) {
    const isConfirm = await confirmAgainBatchDelete()
    if (!isConfirm) return

    const dialog = $q.dialog({
      message: t('common.operationProgress', { progress: 0 }),
      progress: true,
      persistent: true,
      ok: false,
      class: 'flex flex-col items-center justify-center'
    })

    try {
      const total = rows.length
      let successCount = 0
      let failCount = 0
      const startTime = Date.now()

      // 创建删除任务数组，每个任务都包含删除操作和更新进度
      const tasks = rows.map((row, index) => {
        const req = create(SetProviderTokenReqSchema, {
          Code: 4,
          ProviderToken: row,
        })
        return setProviderToken(req)
          .then(res => {
            if (res.Code === 0) {
              successCount++
              deleteDbMapProviderTokenStoreRow(row)
            } else {
              failCount++
            }
            // 更新进度对话框
            const progress = Math.round(((index + 1) / total) * 100)
            dialog.update({
              message: t('common.operationProgress', { progress })
            })
            return res
          })
      })

      // 等待所有删除任务完成
      await Promise.all(tasks)

      // 关闭进度对话框，如果删除时间小600毫秒，则等待600毫秒，避免进度提示闪烁
      if (Date.now() - startTime < 800) {
        await new Promise(resolve => setTimeout(resolve, 600 - (Date.now() - startTime)))
      }
      dialog.hide()

      // 清空选中项
      selected.value = []

      // 显示操作结果
      if (failCount === 0) {
        successMessage(t('common.operationSuccess'))
      } else {
        errorMessage(t('common.operationPartialSuccess', {
          success: successCount,
          fail: failCount
        }))
      }
    } catch (error) {
      dialog.hide()
      errorMessage(t('common.operationFailed') + `: ${error}`)
    }
  }

  // 删除一行数据
  const deleteDbMapProviderToken = async (row: DbMapProviderToken): Promise<boolean> => {
    const req = create(SetProviderTokenReqSchema, {
      Code: 4,
      ProviderToken: row,
    })
    const res = await setProviderToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      deleteDbMapProviderTokenStoreRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  // 更新一行数据
  const updateDbMapProviderToken = async (row: DbMapProviderToken): Promise<boolean> => {
    const req = create(SetProviderTokenReqSchema, {
      Code: 2,
      ProviderToken: row,
    })
    const res = await setProviderToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      updateDbMapProviderTokenStoreRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  // 切换api启用状态
  const toggleDbMapProviderTokenStatus = async (row: DbMapProviderToken, status: number) => {
    const newRow = cloneDeep(row)
    newRow.Status = status
    const req = create(SetProviderTokenReqSchema, {
      Code: 2,
      ProviderToken: newRow,
      UpdateFields: ['Status']
    })
    const res = await setProviderToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      updateDbMapProviderTokenStoreRow(newRow)
      return
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
  }

  // 插入一行数据
  const insertDbMapProviderToken = async (row: DbMapProviderToken): Promise<boolean> => {
    row.CreateTime = getTimestamp()
    const req = create(SetProviderTokenReqSchema, {
      Code: 1,
      ProviderToken: row,
    })
    const res = await setProviderToken(req)
    if (res.Code === 0) {
      successMessage(t('common.operationSuccess'))
      addRow(row)
      return true
    }

    errorMessage(t('common.operationFailed') + ` ${res.Reason}`)
    return false
  }

  async function submit(keepAdd?: boolean) {
    const valid = await formRef.value!.validate()
    if (!valid) return

    let isOk: boolean = false
    if (dialogAction.value === OpenDialogAction.Add) {
      isOk = await insertDbMapProviderToken(dbMapProviderToken.value)
    } else {
      isOk = await updateDbMapProviderToken(dbMapProviderToken.value)
    }

    if (isOk && !keepAdd) {
      visible.value = false
    }

    // 创建新的token数据
    if (keepAdd) {
      dbMapProviderToken.value = getDefaultDbMapProviderToken(dbMapProviderToken.value)
    }
  }

  onMounted(() => {
    StrPubSub.subscribe(NewMapApiKeyAction, () => {
      openDialogBeforeAction(OpenDialogAction.Add)
    })
  })
  onUnmounted(() => {
    StrPubSub.unsubscribe(NewMapApiKeyAction)
  })
</script>

<style lang="scss">
  @import "@/css/data_table.scss";
</style>
