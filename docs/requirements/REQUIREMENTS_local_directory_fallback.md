# 本地目录Fallback机制与语言支持功能需求文档

## 功能概述

在MapHandler方法中实现本地目录fallback机制，当向keyValue数据库请求地图瓦片失败或需要更新时，在NATS请求和在线provider逻辑之前，根据provider类型自动尝试对应的本地目录版本provider类型，从本地文件系统获取地图瓦片。

该机制包含两个核心功能：

1. **基础Fallback机制**：本地文件系统作为缓存和在线服务之间的中间层
2. **语言支持功能**：基于DbMapProviderToken.language字段的精确语言匹配，确保返回正确语言版本的地图瓦片

## 背景分析

### 现有Provider类型

根据`proto/db.proto`中的`MapProviderEnum`定义，系统支持以下6种provider类型：

1. **在线Provider类型**：
   - `ProviderGoogle` (0) - Google地图在线服务
   - `ProviderTianditu` (1) - 天地图在线服务
   - `ProviderOSM` (2) - OpenStreetMap在线服务

2. **本地目录Provider类型**：
   - `ProviderGoogleLocalDirectory` (3) - Google地图本地目录
   - `ProviderTiandituLocalDirectory` (4) - 天地图本地目录
   - `ProviderOSMLocalDirectory` (5) - OSM本地目录

### 语言支持基础设施

根据`proto/db.proto`中的定义，系统已具备以下语言支持基础设施：

1. **DbMapProviderToken.language字段**：
   - 字段类型：`string`
   - 默认值：空字符串
   - 用途：存储本地目录中瓦片的语言信息
   - 注释：仅对ProviderGoogleLocalDirectory有效，ProviderOSMLocalDirectory不支持多语言，ProviderTiandituLocalDirectory仅支持zh-CN

2. **现有语言处理**：
   - 系统在`parseMapReq`方法中已使用`golang.org/x/text/language`库进行语言解析
   - 支持BCP 47格式的语言标签
   - 针对不同provider有特定的语言规则

## 具体需求

### 1. Provider映射关系

建立在线provider与本地目录provider的对应关系：

| 在线Provider | 本地目录Provider | 数值映射 | 语言支持 |
|-------------|------------------|---------|---------|
| ProviderGoogle (0) | ProviderGoogleLocalDirectory (3) | 0 → 3 | 多语言支持 |
| ProviderTianditu (1) | ProviderTiandituLocalDirectory (4) | 1 → 4 | 仅zh-CN |
| ProviderOSM (2) | ProviderOSMLocalDirectory (5) | 2 → 5 | 跳过语言检查 |

### 2. Fallback触发条件

本地文件夹fallback机制作为现有逻辑流程的补充，在以下情况下触发：

- `QueryTileFromKeyValueDb`返回错误（瓦片不存在于数据库）
- 或者`QueryTileFromKeyValueDb`返回的瓦片需要更新（基于缓存时间和Status判断）

**重要说明**：本地目录fallback不会替换现有的NATS请求和在线provider逻辑，而是在这些步骤之前，作为缓存获取失败或需要更新的额外的fallback选项。当从缓存无法获取到正确可用的瓦片时，系统会尝试从本地目录获取瓦片，然后再继续现有的NATS请求和在线provider逻辑。

### 3. 语言匹配机制

#### 3.1 语言代码标准化处理

**基本原则**：

- 使用`golang.org/x/text/language`库进行语言代码标准化
- 支持BCP 47格式的语言标签
- 处理各种语言变体（如中文、英语、葡萄牙语等多种语言都有变体）
- 对于只有语言没有国家的语言标签（如`zh`），将其归一化到对应的BCP 47语言格式

**标准化要求**：

- 语言解析后自动获得标准的BCP 47格式，无需额外的变体格式匹配
- 依赖标准库的BCP 47格式处理，确保语言标签的一致性和准确性

#### 3.2 语言匹配逻辑

**匹配策略**：

1. **精确匹配**：请求语言与token.language完全相同
2. **语言族匹配**：当精确匹配失败时，尝试语言族匹配（如`zh-CN`匹配`zh`）
3. **空语言处理**：token.language为空时视为通用语言，匹配所有请求（向后兼容）

#### 3.3 提供商特定的语言支持规则

**Google地图（ProviderGoogleLocalDirectory）**：

- 支持多语言匹配，需要严格的language字段匹配
- 卫星图层跳过语言检查
- 路线图/混合图层执行语言匹配

**天地图（ProviderTiandituLocalDirectory）**：

- 仅支持zh-CN，使用标准库进行语言变体处理
- 非中文语言请求时跳过该provider
- 卫星图层跳过语言检查

**OSM（ProviderOSMLocalDirectory）**：

- 跳过语言匹配检查，语言由地理位置自动决定
- 所有语言请求都可以使用同一套OSM瓦片

### 4. 本地文件路径格式

根据`proto/db.proto`中的注释，本地文件路径格式为：

- **BaseUrl字段**：存储本地目录的根路径
- **文件路径格式**：`{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}`

具体格式：

- Google卫星图：`{BaseUrl}/satellite/{z}/{x}/{y}.jpg`
- Google混合图：`{BaseUrl}/hybrid/{z}/{x}/{y}.jpg`
- Google路线图：`{BaseUrl}/roadmap/{z}/{x}/{y}.png`
- 天地图和OSM：与Google相同的路径格式

### 5. 实现逻辑

#### 5.1 集成到现有流程

本地目录fallback机制将集成到`MapHttpHandler`的现有处理流程中，具体位置在：

1. **主流程集成点**：在`MapHttpHandler`的`sfg.Do`函数内部
2. **触发时机**：当`QueryTileFromKeyValueDb`返回错误或需要更新瓦片时
3. **执行顺序**：

   ```text
   缓存查询 (QueryTileFromKeyValueDb)
   ↓ (失败或需要更新)
   本地目录Fallback (QueryTileFromLocalDirectory) ← 包含语言匹配
   ↓ (失败)
   NATS请求 (如果启用)
   ↓ (失败)
   在线Provider请求 (QueryTileFromProvider)
   ```

#### 5.2 方法级别的精确实现范围

**resolveTempMapProjectToken方法**：

- **实现要求**：明确排除本地目录provider查询功能
- **技术规范**：该方法专门处理临时项目token，不应包含本地目录fallback逻辑

**handleWithCacheOrOSMMapTile方法**：

- **实现要求**：仅在`sysexpired=1`条件下启用本地目录provider查询
- **触发条件**：在QueryTileFromKeyValueDb失败后，尝试本地目录fallback
- **实现位置**：第205行TODO注释处

**natsHandlerForMapReq方法**：

- **实现要求**：完整实现本地目录provider查询功能，作为NATS请求前的fallback机制
- **实现逻辑**：在QueryTileFromKeyValueDb失败后，尝试本地目录fallback

#### 5.3 本地目录fallback实现

当缓存获取失败或需要更新时，在NATS和在线请求之前，首先执行以下步骤：

1. 根据当前provider类型计算对应的本地目录provider类型
2. 查询该本地目录provider的token配置
3. **语言匹配过滤**：根据请求语言和token语言进行匹配
4. 从本地文件系统读取瓦片文件
5. 如果成功读取，保存到数据库并返回
6. 如果本地目录fallback失败，继续执行现有的NATS和在线provider流程

#### 5.4 核心方法要求

**QueryTileFromLocalDirectory方法**：

- 实现本地目录瓦片查询功能
- 集成语言匹配过滤逻辑
- 支持多个provider token的语言兼容性检查

**Provider类型映射**：

- 实现在线provider到本地目录provider的类型映射
- 支持Google、天地图、OSM三种provider类型

#### 5.5 ReqMapFromProviderWithToken方法扩展

**扩展要求**：添加对本地目录provider类型的支持，使其能够处理本地目录provider类型的瓦片请求。

### 6. 语言匹配机制要求

#### 6.1 语言兼容性检查

**基本要求**：

- 实现语言兼容性检查功能
- 支持向后兼容（空token语言匹配所有请求）
- 卫星图跳过语言检查
- 根据不同provider类型执行相应的匹配逻辑

#### 6.2 语言匹配规则

**Google Provider**：支持多语言匹配，使用标准BCP 47格式进行精确匹配和语言族匹配
**天地图Provider**：仅支持中文，使用标准库处理语言变体
**OSM Provider**：跳过语言匹配检查

### 7. 错误处理和日志记录

#### 7.1 语言代码解析失败

**降级策略**：

- 当语言代码解析失败时，记录警告日志
- 继续尝试其他可用的provider token
- 不中断整个请求流程

#### 7.2 本地文件不存在

**处理策略**：

- 记录调试级别日志，不视为错误
- 继续fallback到NATS或在线provider
- 避免日志污染

#### 7.3 详细调试日志

**日志内容**：

- 语言匹配过程的详细信息
- Provider选择逻辑
- 文件路径构建过程
- 匹配成功/失败的原因

### 8. 性能考虑

- 本地文件读取应该比网络请求更快
- 考虑添加本地文件缓存机制，避免重复读取同一文件
- 文件读取失败时，避免频繁重试
- 语言匹配过程的性能优化

## 实现计划

### 阶段1：核心语言匹配功能

- 语言标准化和匹配逻辑实现
- Provider类型映射功能
- 单元测试覆盖

### 阶段2：方法集成

- QueryTileFromLocalDirectory方法实现
- handleWithCacheOrOSMMapTile方法修改
- natsHandlerForMapReq方法扩展
- ReqMapFromProviderWithToken方法扩展

### 阶段3：测试和优化

- 单元测试和集成测试
- 端到端语言匹配流程测试
- 性能优化

## 测试计划

### 1. 单元测试

- 语言标准化功能测试
- 语言匹配逻辑测试
- Provider类型映射测试
- 基础功能测试

### 2. 集成测试

- 多语言Provider配置测试
- 完整fallback流程测试

## 风险评估

### 技术风险

1. **语言代码标准化复杂性**
   - 风险：BCP 47标准的复杂性可能导致边界情况处理不当
   - 缓解：使用成熟的golang.org/x/text/language库，充分的单元测试

2. **向后兼容性问题**
   - 风险：现有空Language字段的token可能无法正常工作
   - 缓解：实现明确的向后兼容逻辑，空字段匹配所有请求

3. **性能影响**
   - 风险：语言匹配逻辑可能增加请求处理时间
   - 缓解：优化匹配算法，考虑添加缓存机制

4. **本地文件系统IO性能**
   - 风险：本地文件系统IO性能可能成为瓶颈
   - 缓解：性能测试和优化，考虑异步读取

5. **存储空间问题**
   - 风险：大量本地文件可能占用过多磁盘空间
   - 缓解：实现文件清理机制，监控磁盘使用

6. **文件权限问题**
   - 风险：文件权限问题可能导致读取失败
   - 缓解：详细的错误处理和日志记录

### 业务风险

1. **语言匹配错误**
   - 风险：错误的语言匹配可能导致返回错误语言的地图
   - 缓解：详细的测试覆盖，渐进式部署

2. **配置复杂性增加**
   - 风险：语言支持增加了token配置的复杂性
   - 缓解：提供清晰的配置文档和示例

3. **数据一致性问题**
   - 风险：本地文件可能不是最新版本
   - 缓解：实现文件版本检查和更新机制

4. **覆盖范围不完整**
   - 风险：本地文件覆盖范围可能不完整
   - 缓解：监控和报告机制，自动fallback到在线服务

5. **维护成本增加**
   - 风险：需要额外的文件管理和更新机制
   - 缓解：自动化管理工具和监控系统

## 总结

本需求文档详细定义了本地目录fallback机制与语言支持功能的完整技术规范。该功能包含两个核心组件：

### 核心价值

1. **可靠性提升**：通过本地目录fallback机制，在网络服务不可用时仍能提供地图瓦片服务
2. **国际化支持**：基于DbMapProviderToken.language字段的精确语言匹配，确保用户获取到正确语言版本的地图瓦片
3. **性能优化**：本地文件访问比网络请求更快，提升用户体验
4. **向后兼容**：确保现有配置无需修改即可正常工作
5. **可维护性**：清晰的模块化设计和完善的错误处理

### 技术特点

- **严格的语言匹配逻辑**：支持BCP 47标准，针对不同地图提供商的特定规则
- **完善的错误处理**：多层次的fallback机制，确保服务可用性
- **性能优化设计**：语言匹配算法优化，避免性能瓶颈
- **全面的测试策略**：单元测试、集成测试和性能测试的完整覆盖

### 实施建议

实现过程中需要特别注意不同地图提供商的语言支持特性，确保匹配逻辑的准确性和系统的稳定性。通过分阶段的实现计划和全面的测试策略，可以确保功能的质量和可靠性。

该功能将显著提升系统的国际化支持能力和服务可靠性，为用户提供更好的地图服务体验。
