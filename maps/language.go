package maps

import (
	"strings"

	"github.com/rs/zerolog/log"
	"golang.org/x/text/language"
)

// NormalizeLanguageCode uses golang.org/x/text/language to parse and standardize language codes to BCP 47 format
func NormalizeLanguageCode(langCode string) string {
	if langCode == "" {
		return ""
	}

	// Parse the language code
	tag, err := language.Parse(langCode)
	if err != nil {
		log.Debug().Str("langCode", langCode).Err(err).Msg("Failed to parse language code")
		return langCode // Return original if parsing fails
	}

	// Return the canonical BCP 47 format
	return tag.String()
}

// isLanguageCompatible handles different provider-specific language matching rules
func isLanguageCompatible(providerType int32, tokenLanguage, requestLanguage string) bool {
	// Normalize both language codes
	normalizedTokenLang := NormalizeLanguageCode(tokenLanguage)
	normalizedRequestLang := NormalizeLanguageCode(requestLanguage)

	switch providerType {
	case 3: // ProviderGoogleLocalDirectory
		// For Google providers, support multi-language matching with exact and language family matching
		return matchLanguage(normalizedTokenLang, normalizedRequestLang)
	case 4: // ProviderTiandituLocalDirectory
		// For Tianditu providers, only support Chinese language variants
		return matchTiandituLanguage(normalizedTokenLang, normalizedRequestLang)
	case 5: // ProviderOSMLocalDirectory
		// For OSM providers, skip language checks entirely
		return true
	default:
		return false
	}
}

// matchLanguage provides general language matching with exact match and language family fallback
func matchLanguage(tokenLang, requestLang string) bool {
	if tokenLang == "" || requestLang == "" {
		return true // Empty language matches everything
	}

	// Exact match
	if tokenLang == requestLang {
		return true
	}

	// Parse both languages for family matching
	tokenTag, err1 := language.Parse(tokenLang)
	requestTag, err2 := language.Parse(requestLang)

	if err1 != nil || err2 != nil {
		// If parsing fails, fall back to string comparison
		return strings.EqualFold(tokenLang, requestLang)
	}

	// Language family matching (e.g., en-US matches en-GB)
	tokenBase, _ := tokenTag.Base()
	requestBase, _ := requestTag.Base()

	return tokenBase == requestBase
}

// matchTiandituLanguage specifically handles Chinese language variant matching
func matchTiandituLanguage(tokenLang, requestLang string) bool {
	if tokenLang == "" || requestLang == "" {
		return true // Empty language matches everything
	}

	// Parse both languages
	tokenTag, err1 := language.Parse(tokenLang)
	requestTag, err2 := language.Parse(requestLang)

	if err1 != nil || err2 != nil {
		// If parsing fails, check if both contain "zh" or "cn"
		tokenLower := strings.ToLower(tokenLang)
		requestLower := strings.ToLower(requestLang)
		return (strings.Contains(tokenLower, "zh") || strings.Contains(tokenLower, "cn")) &&
			   (strings.Contains(requestLower, "zh") || strings.Contains(requestLower, "cn"))
	}

	// Check if both are Chinese variants
	tokenBase, _ := tokenTag.Base()
	requestBase, _ := requestTag.Base()

	chineseBase := language.Chinese.Base()
	return tokenBase == chineseBase && requestBase == chineseBase
}