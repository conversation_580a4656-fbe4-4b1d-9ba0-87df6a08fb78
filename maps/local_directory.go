package maps

import (
	"bfmap/config"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"

	"github.com/phuslu/log"
)

// getLocalDirectoryProviderType maps online provider types to local directory provider types (0→3, 1→4, 2→5)
func getLocalDirectoryProviderType(onlineProviderType int32) int32 {
	switch onlineProviderType {
	case 0: // ProviderGoogle
		return 3 // ProviderGoogleLocalDirectory
	case 1: // ProviderTianditu
		return 4 // ProviderTiandituLocalDirectory
	case 2: // ProviderOSM
		return 5 // ProviderOSMLocalDirectory
	default:
		return -1 // Invalid provider type
	}
}

// readLocalDirectoryFile constructs file paths and reads file contents
func readLocalDirectoryFile(token *MapProviderToken, mapType string, z, x, y int) ([]byte, error) {
	// Determine file extension based on map type
	var ext string
	switch mapType {
	case "roadmap":
		ext = "png"
	case "satellite", "hybrid":
		ext = "jpg"
	default:
		ext = "png" // Default to PNG
	}

	// Construct file path: {BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
	filePath := filepath.Join(token.BaseUrl, mapType, fmt.Sprintf("%d", z), fmt.Sprintf("%d", x), fmt.Sprintf("%d.%s", y, ext))

	if config.IsVerboseDebugMap {
		log.Debug().
			Str("filePath", filePath).
			Str("mapType", mapType).
			Int("z", z).
			Int("x", x).
			Int("y", y).
			Msg("Attempting to read local directory file")
	}

	// Read file contents
	data, err := os.ReadFile(filePath)
	if err != nil {
		if config.IsVerboseDebugMap {
			log.Debug().
				Str("filePath", filePath).
				Err(err).
				Msg("Failed to read local directory file")
		}
		return nil, err
	}

	if config.IsVerboseDebugMap {
		log.Debug().
			Str("filePath", filePath).
			Int("dataSize", len(data)).
			Msg("Successfully read local directory file")
	}

	return data, nil
}

// QueryTileFromLocalDirectory retrieves provider tokens, filters by language compatibility, and attempts to read files
func QueryTileFromLocalDirectory(providerType int32, mapType string, z, x, y int, language string) ([]byte, error) {
	// Get provider tokens for the local directory provider type
	tokens, err := GetMapProviderTokensWithAdminFallback(config.AdminRid, int(providerType))
	if err != nil || len(tokens) == 0 {
		if config.IsVerboseDebugMap {
			slog.Debug("No provider tokens found for local directory provider",
				"providerType", providerType,
				"err", err)
		}
		return nil, fmt.Errorf("no provider tokens found for provider type %d: %v", providerType, err)
	}

	// Filter tokens by language compatibility
	var compatibleTokens []*MapProviderToken
	for _, token := range tokens {
		if isLanguageCompatible(providerType, token.Language, language) {
			compatibleTokens = append(compatibleTokens, token)
		}
	}

	if len(compatibleTokens) == 0 {
		if config.IsVerboseDebugMap {
			log.Debug().
				Int32("providerType", providerType).
				Str("language", language).
				Msg("No language-compatible tokens found for local directory provider")
		}
		return nil, fmt.Errorf("no language-compatible tokens found for provider type %d with language %s", providerType, language)
	}

	// Attempt to read files from compatible tokens
	for _, token := range compatibleTokens {
		data, err := readLocalDirectoryFile(token, mapType, z, x, y)
		if err != nil {
			if config.IsVerboseDebugMap {
				log.Debug().
					Str("tokenBaseUrl", token.BaseUrl).
					Err(err).
					Msg("Failed to read from local directory token, trying next")
			}
			continue
		}

		// Save successful read to database
		mapReq := &MapReq{
			Provider: int(providerType),
			MapType:  mapType,
			Z:        z,
			X:        x,
			Y:        y,
			Lang:     language,
		}

		_, saveErr := SaveTileToDb(mapReq, data, true)
		if err != nil {
			slog.Warn("Failed to save local directory tile to database",
				"provider", providerType,
				"mapType", mapType,
				"z", z,
				"x", x,
				"y", y,
				"err", err)
		}

		if config.IsVerboseDebugMap {
			log.Debug().
				Str("tokenBaseUrl", token.BaseUrl).
				Int32("providerType", providerType).
				Str("mapType", mapType).
				Int("z", z).
				Int("x", x).
				Int("y", y).
				Msg("Successfully retrieved tile from local directory")
		}

		return data, nil
	}

	return nil, fmt.Errorf("failed to read tile from any compatible local directory token")
}

// tryLocalDirectoryFallback helper function to be shared across different integration points
func tryLocalDirectoryFallback(providerType int32, mapType string, z, x, y int, language string) ([]byte, error) {
	// Map online provider type to local directory provider type
	localProviderType := getLocalDirectoryProviderType(providerType)
	if localProviderType == -1 {
		return nil, fmt.Errorf("invalid provider type for local directory fallback: %d", providerType)
	}

	if config.IsVerboseDebugMap {
		log.Debug().
			Int32("onlineProviderType", providerType).
			Int32("localProviderType", localProviderType).
			Str("mapType", mapType).
			Int("z", z).
			Int("x", x).
			Int("y", y).
			Str("language", language).
			Msg("Attempting local directory fallback")
	}

	// Query tile from local directory
	return QueryTileFromLocalDirectory(localProviderType, mapType, z, x, y, language)
}